import Student from "../models/student.model.js";
import User from "../models/user.model.js";
import bcrypt from "bcryptjs";

// Helper function to hash password
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(12);
  return await bcrypt.hash(password, salt);
};

// Create a new student
export const createStudent = async (req, res) => {
  try {
    const studentData = req.body;

    if (!studentData.schoolId && req.user?.schoolId) {
      studentData.schoolId = req.user.schoolId;
    }

    const existingStudent = await Student.findOne({ email: studentData.email });
    if (existingStudent) {
      return res.status(400).json({
        success: false,
        message: "A student with this email already exists",
      });
    }

    const existingUser = await User.findOne({ email: studentData.email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "A user with this email already exists",
      });
    }

    // Hash password if provided
    if (studentData.password) {
      studentData.password = await hashPassword(studentData.password);
    }

    // Create the student record
    const newStudent = new Student(studentData);
    await newStudent.save();

    // Create corresponding user account if isActive is true
    if (studentData.isActive && studentData.email && studentData.password) {
      const userData = {
        name: `${studentData.firstName} ${studentData.lastName}`,
        email: studentData.email,
        password: await hashPassword(studentData.password),
        role: "student",
        schoolId: req.user?.schoolId || null,
      };
      const newUser = new User(userData);
      await newUser.save();
    }

    const studentResponse = newStudent.toObject();
    delete studentResponse.password;

    res.status(201).json({
      success: true,
      message: "Student created successfully",
      data: studentResponse,
    });
  } catch (error) {
    console.error("Create Student Error:", error);
    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Email or roll number already exists",
      });
    }
    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all students
export const getAllStudents = async (req, res) => {
  try {
    let query = {};
    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }
    if (req.query.schoolId && req.user?.role === "admin") {
      query.schoolId = req.query.schoolId;
    }
    const students = await Student.find(query)
      .select("-password")
      .populate("schoolId", "name")
      .populate("class", "name code")
      .populate("section", "name code")
      .sort({ createdAt: -1 });
    res.status(200).json({
      success: true,
      count: students.length,
      data: students,
    });
  } catch (error) {
    console.error("Get All Students Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get student by ID
export const getStudentById = async (req, res) => {
  try {
    const student = await Student.findById(req.params.id)
      .select("-password")
      .populate("schoolId", "name")
      .populate("class", "name code")
      .populate("section", "name code");
    if (!student) {
      return res.status(404).json({
        success: false,
        message: "Student not found",
      });
    }
    res.status(200).json({
      success: true,
      data: student,
    });
  } catch (error) {
    console.error("Get Student By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update student
export const updateStudent = async (req, res) => {
  try {
    const studentData = req.body;
    const studentId = req.params.id;
    if (!studentData.schoolId && req.user?.schoolId) {
      studentData.schoolId = req.user.schoolId;
    }
    const existingStudent = await Student.findById(studentId);
    if (!existingStudent) {
      return res.status(404).json({
        success: false,
        message: "Student not found",
      });
    }
    if (studentData.email && studentData.email !== existingStudent.email) {
      const emailExists = await Student.findOne({
        email: studentData.email,
        _id: { $ne: studentId },
      });
      if (emailExists) {
        return res.status(400).json({
          success: false,
          message: "A student with this email already exists",
        });
      }
      const userEmailExists = await User.findOne({ email: studentData.email });
      if (userEmailExists) {
        return res.status(400).json({
          success: false,
          message: "A user with this email already exists",
        });
      }
    }
    if (studentData.password === "") {
      delete studentData.password;
    } else if (studentData.password) {
      studentData.password = await hashPassword(studentData.password);
    }
    const student = await Student.findByIdAndUpdate(studentId, studentData, {
      new: true,
      runValidators: true,
    })
      .select("-password")
      .populate("schoolId", "name")
      .populate("class", "name code")
      .populate("section", "name code");
    if (!student) {
      return res.status(404).json({
        success: false,
        message: "Student not found",
      });
    }
    const existingUser = await User.findOne({ email: existingStudent.email });
    if (existingUser) {
      const userUpdateData = {
        name: `${studentData.firstName || existingStudent.firstName} ${
          studentData.lastName || existingStudent.lastName
        }`,
        email: studentData.email || existingStudent.email,
      };
      if (studentData.password) {
        userUpdateData.password = await hashPassword(studentData.password);
      }
      await User.findByIdAndUpdate(existingUser._id, userUpdateData);
    } else if (
      studentData.isActive &&
      studentData.email &&
      studentData.password
    ) {
      const userData = {
        name: `${studentData.firstName || existingStudent.firstName} ${
          studentData.lastName || existingStudent.lastName
        }`,
        email: studentData.email || existingStudent.email,
        password: await hashPassword(studentData.password),
        role: "student",
        schoolId: req.user?.schoolId || null,
      };
      const newUser = new User(userData);
      await newUser.save();
    }
    res.status(200).json({
      success: true,
      message: "Student updated successfully",
      data: student,
    });
  } catch (error) {
    console.error("Update Student Error:", error);
    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Email or roll number already exists",
      });
    }
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete student
export const deleteStudent = async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: "Student not found",
      });
    }
    await User.findOneAndDelete({ email: student.email });
    await Student.findByIdAndDelete(req.params.id);
    res.status(200).json({
      success: true,
      message: "Student deleted successfully",
    });
  } catch (error) {
    console.error("Delete Student Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update student status
export const updateStudentStatus = async (req, res) => {
  try {
    const { status } = req.body;
    if (!status || !["active", "inactive", "alumni"].includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Please provide a valid status",
      });
    }
    const student = await Student.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    )
      .select("-password")
      .populate("schoolId", "name")
      .populate("class", "name code")
      .populate("section", "name code");
    if (!student) {
      return res.status(404).json({
        success: false,
        message: "Student not found",
      });
    }
    res.status(200).json({
      success: true,
      message: "Student status updated successfully",
      data: student,
    });
  } catch (error) {
    console.error("Update Student Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Bulk promote students
export const bulkPromoteStudents = async (req, res) => {
  try {
    const { studentIds, targetClass, targetSection } = req.body;

    if (!studentIds || studentIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Please provide valid student IDs",
      });
    }

    if (!targetClass || !targetSection) {
      return res.status(400).json({
        success: false,
        message: "Please provide target class and section",
      });
    }

    // Update all students in bulk
    const result = await Student.updateMany(
      { _id: { $in: studentIds } },
      {
        class: targetClass,
        section: targetSection,
      },
      { runValidators: true }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({
        success: false,
        message: "No students found with the provided IDs",
      });
    }

    // Fetch updated students to return
    const updatedStudents = await Student.find({ _id: { $in: studentIds } })
      .select("-password")
      .populate("schoolId", "name")
      .populate("class", "name code")
      .populate("section", "name code");

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} student(s) promoted successfully`,
      data: updatedStudents,
      promotedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Bulk Promote Students Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
