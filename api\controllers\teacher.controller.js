import Teacher from "../models/teacher.model.js";
import User from "../models/user.model.js";
import bcrypt from "bcryptjs";

// Helper function to hash password
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(12);
  return await bcrypt.hash(password, salt);
};

// Helper function to compare password
export const comparePassword = async (candidatePassword, hashedPassword) => {
  return await bcrypt.compare(candidatePassword, hashedPassword);
};

// Create a new teacher
export const createTeacher = async (req, res) => {
  try {
    const teacherData = req.body;

    if (!teacherData.schoolId && req.user?.schoolId) {
      teacherData.schoolId = req.user.schoolId;
    }

    const existingTeacher = await Teacher.findOne({ email: teacherData.email });
    if (existingTeacher) {
      return res.status(400).json({
        success: false,
        message: "A teacher with this email already exists",
      });
    }

    const existingUser = await User.findOne({ email: teacherData.email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "A user with this email already exists",
      });
    }

    // Hash password if provided
    if (teacherData.password) {
      teacherData.password = await hashPassword(teacherData.password);
    }

    // Create the teacher record
    const newTeacher = new Teacher(teacherData);
    await newTeacher.save();

    // Create corresponding user account if isActive is true
    if (teacherData.isActive && teacherData.email && teacherData.password) {
      const userData = {
        name: `${teacherData.firstName} ${teacherData.lastName}`,
        email: teacherData.email,
        password: await hashPassword(teacherData.password),
        role: "teacher",
        schoolId: req.user?.schoolId || null,
      };

      const newUser = new User(userData);
      await newUser.save();
    }

    const teacherResponse = newTeacher.toObject();
    delete teacherResponse.password;

    res.status(201).json({
      success: true,
      message: "Teacher created successfully",
      data: teacherResponse,
    });
  } catch (error) {
    console.error("Create Teacher Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Email already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all teachers
export const getAllTeachers = async (req, res) => {
  try {
    let query = {};

    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    if (req.query.schoolId && req.user?.role === "admin") {
      query.schoolId = req.query.schoolId;
    }

    const teachers = await Teacher.find(query)
      .select("-password")
      .populate("schoolId", "name")
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: teachers.length,
      data: teachers,
    });
  } catch (error) {
    console.error("Get All Teachers Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get teacher by ID
export const getTeacherById = async (req, res) => {
  try {
    const teacher = await Teacher.findById(req.params.id)
      .select("-password")
      .populate("schoolId", "name");

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: "Teacher not found",
      });
    }

    res.status(200).json({
      success: true,
      data: teacher,
    });
  } catch (error) {
    console.error("Get Teacher By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get current teacher's profile
export const getCurrentTeacherProfile = async (req, res) => {
  try {
    const teacher = await Teacher.findOne({ email: req.user.email })
      .select("-password")
      .populate("schoolId", "name")
      .populate({
        path: "assignedSections",
        select: "name code classId teacherId",
        populate: {
          path: "classId",
          select: "name code academicYear description isActive",
          populate: {
            path: "department",
            select: "name code",
          },
        },
      })
      .populate("department", "name code")
      .populate("primarySubject", "name code")
      .populate("secondarySubject", "name code");

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: "Teacher profile not found",
      });
    }

    res.status(200).json({
      success: true,
      data: teacher,
    });
  } catch (error) {
    console.error("Get Current Teacher Profile Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update teacher
export const updateTeacher = async (req, res) => {
  try {
    const teacherData = req.body;
    const teacherId = req.params.id;

    // Add schoolId from authenticated user if not provided
    if (!teacherData.schoolId && req.user?.schoolId) {
      teacherData.schoolId = req.user.schoolId;
    }

    const existingTeacher = await Teacher.findById(teacherId);
    if (!existingTeacher) {
      return res.status(404).json({
        success: false,
        message: "Teacher not found",
      });
    }

    if (teacherData.email && teacherData.email !== existingTeacher.email) {
      const emailExists = await Teacher.findOne({
        email: teacherData.email,
        _id: { $ne: teacherId },
      });
      if (emailExists) {
        return res.status(400).json({
          success: false,
          message: "A teacher with this email already exists",
        });
      }

      const userEmailExists = await User.findOne({
        email: teacherData.email,
      });
      if (userEmailExists) {
        return res.status(400).json({
          success: false,
          message: "A user with this email already exists",
        });
      }
    }

    if (teacherData.password === "") {
      delete teacherData.password;
    } else if (teacherData.password) {
      teacherData.password = await hashPassword(teacherData.password);
    }

    const teacher = await Teacher.findByIdAndUpdate(teacherId, teacherData, {
      new: true,
      runValidators: true,
    })
      .select("-password")
      .populate("schoolId", "name");

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: "Teacher not found",
      });
    }

    const existingUser = await User.findOne({ email: existingTeacher.email });
    if (existingUser) {
      const userUpdateData = {
        name: `${teacherData.firstName || existingTeacher.firstName} ${
          teacherData.lastName || existingTeacher.lastName
        }`,
        email: teacherData.email || existingTeacher.email,
      };

      // Only update password if provided
      if (teacherData.password) {
        userUpdateData.password = await hashPassword(teacherData.password);
      }

      await User.findByIdAndUpdate(existingUser._id, userUpdateData);
    } else if (
      teacherData.isActive &&
      teacherData.email &&
      teacherData.password
    ) {
      // Create user account if it doesn't exist and teacher is active
      const userData = {
        name: `${teacherData.firstName || existingTeacher.firstName} ${
          teacherData.lastName || existingTeacher.lastName
        }`,
        email: teacherData.email || existingTeacher.email,
        password: await hashPassword(teacherData.password),
        role: "teacher",
        schoolId: req.user?.schoolId || null,
      };

      const newUser = new User(userData);
      await newUser.save();
    }

    res.status(200).json({
      success: true,
      message: "Teacher updated successfully",
      data: teacher,
    });
  } catch (error) {
    console.error("Update Teacher Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Email already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete teacher
export const deleteTeacher = async (req, res) => {
  try {
    const teacher = await Teacher.findById(req.params.id);

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: "Teacher not found",
      });
    }

    await User.findOneAndDelete({ email: teacher.email });
    await Teacher.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: "Teacher deleted successfully",
    });
  } catch (error) {
    console.error("Delete Teacher Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update teacher status
export const updateTeacherStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (!status || !["active", "inactive"].includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Please provide a valid status",
      });
    }

    const teacher = await Teacher.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    )
      .select("-password")
      .populate("schoolId", "name");

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: "Teacher not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Teacher status updated successfully",
      data: teacher,
    });
  } catch (error) {
    console.error("Update Teacher Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get students in teacher's assigned sections for a specific class
export const getMyClassStudents = async (req, res) => {
  try {
    const { classId } = req.params;

    // First get the teacher with their assigned sections
    const teacher = await Teacher.findOne({ email: req.user.email }).populate({
      path: "assignedSections",
      select: "classId",
      match: { classId: classId },
    });

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: "Teacher not found",
      });
    }

    // Check if teacher has any sections in this class
    if (!teacher.assignedSections || teacher.assignedSections.length === 0) {
      return res.status(403).json({
        success: false,
        message: "You are not assigned to any sections in this class",
      });
    }

    // Get section IDs for this class
    const sectionIds = teacher.assignedSections.map((section) => section._id);

    // Get students from the teacher's assigned sections in this class
    const Student = (await import("../models/student.model.js")).default;
    const students = await Student.find({
      class: classId,
      section: { $in: sectionIds },
      status: "active",
    })
      .select("-password")
      .populate("class", "name code")
      .populate("section", "name code")
      .sort({ firstName: 1, lastName: 1 });

    res.status(200).json({
      success: true,
      count: students.length,
      data: students,
    });
  } catch (error) {
    console.error("Get My Class Students Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
