import { Button } from "@/components/ui/button";
import { CardHeader } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { eventTypes } from "@/utils/form-options";

export const CalendarControls = ({
  view,
  setView,
  filterType,
  setFilterType,
  getViewTitle,
  setCurrentDate,
  setSelectedDate,
}) => {
  const navigateDate = (direction) => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      if (view === "month") {
        if (direction === "prev") {
          newDate.setMonth(prev.getMonth() - 1);
        } else {
          newDate.setMonth(prev.getMonth() + 1);
        }
      } else if (view === "week") {
        if (direction === "prev") {
          newDate.setDate(prev.getDate() - 7);
        } else {
          newDate.setDate(prev.getDate() + 7);
        }
      } else if (view === "day") {
        if (direction === "prev") {
          newDate.setDate(prev.getDate() - 1);
        } else {
          newDate.setDate(prev.getDate() + 1);
        }
      }
      return newDate;
    });
  };

  return (
    <CardHeader className="px-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 w-full">
        <div className="grid grid-cols-1 sm:grid-cols-[auto_1fr_auto] gap-2 items-center">
          <div className="flex items-center justify-center sm:justify-start">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateDate("prev")}
              className="mr-2"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-lg sm:text-xl font-semibold min-w-[200px] sm:min-w-[250px] text-center px-2">
              {getViewTitle()}
            </h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateDate("next")}
              className="ml-2"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex justify-center sm:justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const today = new Date();
                setCurrentDate(today);
                setSelectedDate(today);
              }}
              className="w-full sm:w-auto"
            >
              Today
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-[1fr_auto] gap-4 items-center">
          <div className="flex items-center justify-center">
            <Tabs
              value={view}
              onValueChange={setView}
              className="w-full sm:w-auto"
            >
              <TabsList className="grid w-full grid-cols-3 sm:w-auto">
                <TabsTrigger value="month" className="flex-1 sm:flex-none">
                  Month
                </TabsTrigger>
                <TabsTrigger value="week" className="flex-1 sm:flex-none">
                  Week
                </TabsTrigger>
                <TabsTrigger value="day" className="flex-1 sm:flex-none">
                  Day
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="flex justify-center sm:justify-end">
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder="Filter events" />
              </SelectTrigger>
              <SelectContent>
                {eventTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </CardHeader>
  );
};
