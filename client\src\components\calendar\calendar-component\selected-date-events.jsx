import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  CalendarIcon,
  Clock,
  MapPin,
  Users,
  Eye,
  Edit,
  Trash2,
} from "lucide-react";

export const SelectedDateEvents = ({
  selectedDate,
  filteredEvents,
  onEventClick,
  getEventsForDate,
  formatDate,
  formatTime,
  getEventTypeColor,
  getEventTypeIcon,
  getStatusBadgeVariant,
}) => {
  const dayEvents = getEventsForDate(selectedDate);

  return (
    <Card className="h-fit">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <CalendarIcon className="h-5 w-5 text-primary" />
          <div>
            <div className="text-lg">{formatDate(selectedDate)}</div>
            <div className="text-sm font-normal text-muted-foreground">
              {dayEvents.length} {dayEvents.length === 1 ? "event" : "events"}
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {dayEvents.length === 0 ? (
          <div className="text-center py-12 text-muted-foreground">
            <div className="rounded-full bg-muted p-4 w-fit mx-auto mb-4">
              <CalendarIcon className="h-8 w-8 opacity-50" />
            </div>
            <h3 className="font-medium mb-2">No events scheduled</h3>
            <p className="text-sm">No events found for this day</p>
          </div>
        ) : (
          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-4">
              {dayEvents.map((event) => (
                <div
                  key={event._id}
                  className="border rounded-lg p-4 hover:bg-muted/50 transition-colors cursor-pointer"
                  onClick={() => onEventClick(event)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-start gap-3">
                      <div
                        className={`p-2 rounded-lg ${getEventTypeColor(
                          event.eventType
                        )}`}
                      >
                        {getEventTypeIcon(event.eventType)}
                      </div>
                      <div>
                        <h3 className="font-semibold text-base leading-tight">
                          {event.title}
                        </h3>
                        <Badge
                          variant={getStatusBadgeVariant(event.status)}
                          className="mt-1"
                        >
                          {event.status}
                        </Badge>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <span className="sr-only">Actions</span>
                          <span className="text-lg">⋮</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            onEventClick(event);
                          }}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            window.open(
                              `/dashboard/schedules/events/${event._id}/edit`,
                              "_blank"
                            );
                          }}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Event
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Event
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="space-y-2">
                    {!event.isAllDayEvent && event.startTime && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>
                          {formatTime(event.startTime)}
                          {event.endTime && ` - ${formatTime(event.endTime)}`}
                        </span>
                      </div>
                    )}

                    {event.isAllDayEvent && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>All Day Event</span>
                      </div>
                    )}

                    {event.venueName && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>{event.venueName}</span>
                      </div>
                    )}

                    {event.attendeeType && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Users className="h-4 w-4" />
                        <span className="capitalize">
                          {event.attendeeType.replace("_", " ")}
                        </span>
                      </div>
                    )}
                  </div>

                  {event.description && (
                    <p className="text-sm text-muted-foreground mt-3 leading-relaxed line-clamp-2">
                      {event.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
};
