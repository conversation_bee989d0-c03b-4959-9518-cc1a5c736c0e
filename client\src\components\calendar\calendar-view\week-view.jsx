import { formatTime } from "@/utils/format-options";
import {
  getEventTypeColor,
  getEventPosition,
  getTimeSlots,
  getWeekDays,
  isToday,
} from "@/utils/get-functions";

export const WeekView = ({ currentDate, onEventClick, getEventsForDate }) => {
  const weekDays = getWeekDays(currentDate);
  const timeSlots = getTimeSlots();
  const weekDaysShort = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const getEventBackgroundColor = (eventType) => {
    const colorClass = getEventTypeColor(eventType);
    if (colorClass.includes("red")) return "#ef4444";
    if (colorClass.includes("blue")) return "#3b82f6";
    if (colorClass.includes("green")) return "#10b981";
    if (colorClass.includes("purple")) return "#8b5cf6";
    if (colorClass.includes("orange")) return "#f59e0b";
    if (colorClass.includes("yellow")) return "#eab308";
    if (colorClass.includes("pink")) return "#ec4899";
    return "#6366f1";
  };

  return (
    <div className="space-y-4">
      {/* Week Header */}
      <div className="grid grid-cols-8 gap-1">
        <div className="p-2"></div>
        {weekDays.map((day, index) => {
          const isCurrentDay = isToday(day);
          return (
            <div
              key={day.toISOString()}
              className={`p-3 text-center border rounded-lg ${
                isCurrentDay
                  ? "bg-primary/10 border-primary/30 text-primary"
                  : "border-border"
              }`}
            >
              <div className="font-semibold text-sm">
                {weekDaysShort[index]}
              </div>
              <div
                className={`text-lg font-bold ${
                  isCurrentDay ? "text-primary" : "text-foreground"
                }`}
              >
                {day.getDate()}
              </div>
            </div>
          );
        })}
      </div>

      {/* Week Grid */}
      <div className="relative overflow-x-auto">
        <div className="min-w-[800px]">
          <div className="grid grid-cols-8 gap-1">
            {/* Time Column */}
            <div className="space-y-0">
              {timeSlots.map((time) => (
                <div
                  key={time}
                  className="h-16 flex items-start justify-end pr-2 text-xs text-muted-foreground"
                >
                  {formatTime(time)}
                </div>
              ))}
            </div>

            {/* Day Columns */}
            {weekDays.map((day) => (
              <div
                key={day.toISOString()}
                className="relative border-l border-border"
              >
                {/* Time Grid Lines */}
                {timeSlots.map((time) => (
                  <div
                    key={time}
                    className="h-16 border-b border-border/50"
                  ></div>
                ))}

                {/* Events */}
                {getEventsForDate(day).map((event) => {
                  const position = getEventPosition(event);
                  return (
                    <div
                      key={event._id}
                      className="absolute left-1 right-1 rounded p-1 text-xs font-medium shadow-sm cursor-pointer hover:shadow-md transition-shadow z-10"
                      style={{
                        backgroundColor: getEventBackgroundColor(
                          event.eventType
                        ),
                        color: "white",
                        top: position.top,
                        height: position.height,
                        minHeight: "30px",
                      }}
                      onClick={() => onEventClick(event)}
                    >
                      <div className="truncate font-medium">{event.title}</div>
                      {event.startTime && (
                        <div className="text-xs opacity-90">
                          {formatTime(event.startTime)}
                          {event.endTime && ` - ${formatTime(event.endTime)}`}
                        </div>
                      )}
                      {event.venueName && (
                        <div className="text-xs opacity-75 truncate">
                          {event.venueName}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
