import { createContext, useContext, useState, useEffect } from "react";
import {
  createTeacher,
  getTeachers,
  getTeacherById,
  updateTeacher,
  deleteTeacher,
  updateTeacherStatus,
  getCurrentTeacherProfile,
  getMyClassStudents,
  getMyAssignedSectionStudents,
} from "@/api/teacher-api";

const TeacherContext = createContext({
  teachers: [],
  currentTeacher: null,
  students: [],
  isLoading: false,
  loadingStudents: false,
  error: null,
  addTeacher: () => {},
  fetchAllTeachers: () => {},
  fetchTeacherById: () => {},
  editTeacher: () => {},
  removeTeacher: () => {},
  updateStatus: () => {},
  fetchCurrentTeacherProfile: () => {},
  fetchMyClassStudents: () => {},
  initializeTeacherSectionsData: () => {},
  refreshTeacherSectionsData: () => {},
});

export const TeacherProvider = ({ children }) => {
  const [teachers, setTeachers] = useState([]);
  const [currentTeacher, setCurrentTeacher] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [teacherOptions, setTeacherOptions] = useState([]);

  useEffect(() => {
    const options = teachers.map((teacher) => ({
      label: `${teacher.firstName} ${teacher.lastName}`,
      value: teacher._id,
    }));
    setTeacherOptions(options);
  }, [teachers]);

  const addTeacher = async (teacherData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createTeacher(teacherData);
      setTeachers((prevTeachers) => [...prevTeachers, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create teacher");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllTeachers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getTeachers();
      setTeachers(response.data || []);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || "Failed to fetch teachers");
      setTeachers([]);
      setIsLoading(false);
      throw error;
    }
  };

  const fetchTeacherById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getTeacherById(id);
      const teacherData = data.data || data;
      setCurrentTeacher(teacherData);
      setIsLoading(false);
      return teacherData;
    } catch (error) {
      setError(error.message || `Failed to fetch teacher with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editTeacher = async (id, teacherData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateTeacher(id, teacherData);
      setTeachers(
        teachers.map((teacher) =>
          teacher._id === id ? { ...teacher, ...response.data } : teacher
        )
      );
      if (currentTeacher && currentTeacher._id === id) {
        setCurrentTeacher({ ...currentTeacher, ...response.data });
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update teacher with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeTeacher = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteTeacher(id);
      setTeachers(teachers.filter((teacher) => teacher._id !== id));
      if (currentTeacher && currentTeacher._id === id) {
        setCurrentTeacher(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete teacher with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, statusInfo) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await updateTeacherStatus(id, statusInfo.status);
      setTeachers(
        teachers.map((teacher) =>
          teacher._id === id
            ? { ...teacher, status: data.data.status }
            : teacher
        )
      );
      if (currentTeacher && currentTeacher._id === id) {
        setCurrentTeacher({ ...currentTeacher, status: data.data.status });
      }
      setIsLoading(false);
      return data;
    } catch (error) {
      setError(
        error.message || `Failed to update status for teacher with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const assignClassesToTeacher = async (id, classIds) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await assignClasses(id, classIds);
      setTeachers(
        teachers.map((teacher) =>
          teacher._id === id
            ? { ...teacher, assignedClasses: data.data.assignedClasses }
            : teacher
        )
      );
      if (currentTeacher && currentTeacher._id === id) {
        setCurrentTeacher({
          ...currentTeacher,
          assignedClasses: data.data.assignedClasses,
        });
      }
      setIsLoading(false);
      return data;
    } catch (error) {
      setError(
        error.message || `Failed to assign classes to teacher with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const fetchCurrentTeacherProfile = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getCurrentTeacherProfile();
      const teacherData = response.data || response;
      setCurrentTeacher(teacherData);
      setIsLoading(false);
      return teacherData;
    } catch (error) {
      setError(error.message || "Failed to fetch current teacher profile");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchMyClassStudents = async (classId) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getMyClassStudents(classId);
      setIsLoading(false);
      return response.data || response;
    } catch (error) {
      setError(
        error.message || `Failed to fetch students for class: ${classId}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  // Combined initialization method for teacher sections page
  const initializeTeacherSectionsData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // First fetch teacher profile
      const teacherData = await fetchCurrentTeacherProfile();

      // Then fetch students with separate loading state
      setLoadingStudents(true);
      try {
        const response = await getMyAssignedSectionStudents();
        const studentsData = response.data || response;
        setStudents(studentsData);
        setLoadingStudents(false);
        return {
          teacher: teacherData,
          students: studentsData,
        };
      } catch (studentError) {
        console.warn(
          "Failed to fetch students from assigned sections:",
          studentError
        );
        setStudents([]);
        setLoadingStudents(false);
        return {
          teacher: teacherData,
          students: [],
        };
      }
    } catch (error) {
      console.error("Error loading teacher profile:", error);
      setIsLoading(false);
      setLoadingStudents(false);
      throw error;
    }
  };

  // Refresh method for teacher sections page
  const refreshTeacherSectionsData = async () => {
    try {
      setLoadingStudents(true);
      const teacherData = await fetchCurrentTeacherProfile();
      try {
        const response = await getMyAssignedSectionStudents();
        const studentsData = response.data || response;
        setStudents(studentsData);
        setLoadingStudents(false);
        return {
          teacher: teacherData,
          students: studentsData,
        };
      } catch (studentError) {
        console.warn(
          "Failed to fetch students from assigned sections:",
          studentError
        );
        setStudents([]);
        setLoadingStudents(false);
        return {
          teacher: teacherData,
          students: [],
        };
      }
    } catch (error) {
      console.error("Error refreshing data:", error);
      setLoadingStudents(false);
      throw error;
    }
  };

  return (
    <TeacherContext.Provider
      value={{
        teachers,
        setTeachers,
        currentTeacher,
        students,
        isLoading,
        loadingStudents,
        error,
        addTeacher,
        fetchAllTeachers,
        fetchTeacherById,
        editTeacher,
        removeTeacher,
        updateStatus,
        assignClassesToTeacher,
        fetchCurrentTeacherProfile,
        fetchMyClassStudents,
        initializeTeacherSectionsData,
        refreshTeacherSectionsData,
        teacherOptions,
      }}
    >
      {children}
    </TeacherContext.Provider>
  );
};

export const useTeacher = () => useContext(TeacherContext);
