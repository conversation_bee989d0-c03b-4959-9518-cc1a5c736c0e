import express from "express";
import {
  createStudent,
  getAllStudents,
  getStudentById,
  updateStudent,
  deleteStudent,
  updateStudentStatus,
  bulkPromoteStudents,
} from "../controllers/student.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createStudent
);

router.get(
  "/",
  protect,
  authorize(["admin", "school-admin", "teacher"]),
  getAllStudents
);

router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher", "student", "parent"]),
  getStudentById
);

router.put(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  updateStudent
);

router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteStudent
);

router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateStudentStatus
);

router.patch(
  "/bulk-promote",
  protect,
  authorize(["admin", "school-admin"]),
  bulkPromoteStudents
);

export default router;
