import { CardContent } from "@/components/ui/card";
import { MonthView } from "@/components/calendar/calendar-view/month-view";
import { WeekView } from "@/components/calendar/calendar-view/week-view";
import { DayView } from "@/components/calendar/calendar-view/day-view";

export const CalendarContent = ({
  view,
  isLoading,
  currentDate,
  setSelectedDate,
  onEventClick,
  getEventsForDate,
  isSelected,
}) => {
  const renderMonthView = () => {
    return (
      <MonthView
        currentDate={currentDate}
        setSelectedDate={setSelectedDate}
        onEventClick={onEventClick}
        getEventsForDate={getEventsForDate}
        isSelected={isSelected}
      />
    );
  };

  const renderWeekView = () => {
    return (
      <WeekView
        currentDate={currentDate}
        onEventClick={onEventClick}
        getEventsForDate={getEventsForDate}
      />
    );
  };

  const renderDayView = () => {
    return (
      <DayView
        currentDate={currentDate}
        onEventClick={onEventClick}
        getEventsForDate={getEventsForDate}
      />
    );
  };

  return (
    <CardContent className="px-6">
      {isLoading ? (
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading calendar...</p>
          </div>
        </div>
      ) : (
        <>
          {view === "month" && renderMonthView()}
          {view === "week" && renderWeekView()}
          {view === "day" && renderDayView()}
        </>
      )}
    </CardContent>
  );
};
