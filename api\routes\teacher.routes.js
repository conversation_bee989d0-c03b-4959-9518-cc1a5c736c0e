import express from "express";
import {
  createTeacher,
  getAllTeachers,
  getTeacher<PERSON>yId,
  updateTeacher,
  deleteTeacher,
  updateTeacherStatus,
  getCurrentTeacherProfile,
  getMyClassStudents,
  getMyAssignedSectionStudents,
} from "../controllers/teacher.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createTeacher
);
router.get("/", protect, authorize(["admin", "school-admin"]), getAllTeachers);
router.get(
  "/profile/me",
  protect,
  authorize(["teacher"]),
  getCurrentTeacherProfile
);
router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  getTeacherById
);
router.put(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  updateTeacher
);
router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteTeacher
);
router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateTeacherStatus
);
router.get(
  "/my-classes/:classId/students",
  protect,
  authorize(["teacher"]),
  getMyClassStudents
);

router.get(
  "/my-assigned-sections/students",
  protect,
  authorize(["teacher"]),
  getMyAssignedSectionStudents
);

export default router;
