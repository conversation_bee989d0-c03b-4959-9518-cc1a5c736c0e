import {
  getDaysInMonth,
  getEventTypeColor,
  getEventTypeIcon,
  isToday,
} from "@/utils/get-functions";

export const MonthView = ({
  currentDate,
  setSelectedDate,
  onEventClick,
  getEventsForDate,
  isSelected,
}) => {
  const days = getDaysInMonth(currentDate);
  const weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  return (
    <div className="space-y-2">
      {/* Week day headers */}
      <div className="grid grid-cols-7 gap-1">
        {weekDays.map((day) => (
          <div
            key={day}
            className="p-3 text-center font-semibold text-muted-foreground text-sm"
          >
            {day}
          </div>
        ))}
      </div>

      {/* Calendar days */}
      <div className="grid grid-cols-7 gap-1">
        {days.map((date, index) => {
          const dayEvents = getEventsForDate(date);
          return (
            <div
              key={index}
              className={`min-h-[120px] p-2 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                !date ? "bg-muted/20 cursor-not-allowed" : ""
              } ${
                isToday(date)
                  ? "bg-primary/10 border-primary/50 ring-1 ring-primary/20"
                  : "border-border"
              } ${isSelected(date) ? "bg-primary/20 border-primary" : ""}`}
              onClick={() => date && setSelectedDate(date)}
            >
              {date && (
                <>
                  <div
                    className={`text-sm font-semibold mb-2 ${
                      isToday(date) ? "text-primary" : "text-foreground"
                    }`}
                  >
                    {date.getDate()}
                  </div>
                  <div className="space-y-1">
                    {dayEvents.slice(0, 2).map((event) => (
                      <div
                        key={event._id}
                        className={`text-xs p-1.5 rounded border ${getEventTypeColor(
                          event.eventType
                        )} truncate font-medium cursor-pointer hover:opacity-80 transition-opacity`}
                        title={event.title}
                        onClick={(e) => {
                          e.stopPropagation();
                          onEventClick(event);
                        }}
                      >
                        <div className="flex items-center gap-1">
                          {getEventTypeIcon(event.eventType)}
                          <span className="truncate">{event.title}</span>
                        </div>
                      </div>
                    ))}
                    {dayEvents.length > 2 && (
                      <div className="text-xs text-muted-foreground font-medium">
                        +{dayEvents.length - 2} more
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
