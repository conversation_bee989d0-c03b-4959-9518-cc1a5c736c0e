import React, { useState, useEffect } from "react";
import { Container } from "@/components/ui/container";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Users,
  BookOpen,
  GraduationCap,
  Clock,
  Calendar,
  FileText,
  UserCheck,
  AlertCircle,
  ChevronRight,
  RefreshCcwIcon,
  Search,
  Layers,
} from "lucide-react";
import { useTeacher } from "@/context/teacher-context";
import { useStudent } from "@/context/student-context";
import { StatCard } from "@/components/dashboard/stat-card";
import { Link } from "react-router-dom";

const AssignedSections = () => {
  const { currentTeacher, fetchCurrentTeacherProfile, isLoading } =
    useTeacher();
  const { students, fetchAllStudents } = useStudent();

  const [selectedSection, setSelectedSection] = useState(null);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [studentSearchQuery, setStudentSearchQuery] = useState("");

  // Extract assigned sections from teacher profile
  const assignedSections = currentTeacher?.assignedSections || [];

  // Get students for selected section with search filter
  const sectionStudents =
    selectedSection && students
      ? students.filter((student) => {
          const matchesSection =
            student.section?._id === selectedSection._id ||
            student.section === selectedSection._id;

          if (!matchesSection || student.status !== "active") return false;

          if (!studentSearchQuery) return true;

          const query = studentSearchQuery.toLowerCase();
          const fullName =
            `${student.firstName} ${student.lastName}`.toLowerCase();
          const email = student.email?.toLowerCase() || "";
          const rollNumber = student.rollNumber?.toLowerCase() || "";

          return (
            fullName.includes(query) ||
            email.includes(query) ||
            rollNumber.includes(query)
          );
        })
      : [];

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        await fetchCurrentTeacherProfile();
        setLoadingStudents(true);
        try {
          await fetchAllStudents();
        } catch (studentError) {
          console.warn(
            "Teacher not authorized to fetch all students:",
            studentError
          );
        }
        setLoadingStudents(false);
      } catch (error) {
        console.error("Error loading teacher profile:", error);
        setLoadingStudents(false);
      }
    };

    loadData();
  }, []);

  // Auto-select first section if available
  useEffect(() => {
    if (assignedSections.length > 0 && !selectedSection) {
      setSelectedSection(assignedSections[0]);
    }
  }, [assignedSections, selectedSection]);

  const handleSectionSelect = (sectionData) => {
    setSelectedSection(sectionData);
    setStudentSearchQuery("");
  };

  const handleRefresh = async () => {
    try {
      setLoadingStudents(true);
      await fetchCurrentTeacherProfile();
      try {
        await fetchAllStudents();
      } catch (studentError) {
        console.warn(
          "Teacher not authorized to fetch all students:",
          studentError
        );
      }
    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      setLoadingStudents(false);
    }
  };

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          title="My Assigned Sections"
          description="Manage your assigned sections and students"
          isLoading={isLoading}
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "My Sections" },
          ]}
          actions={[
            {
              label: "Refresh",
              icon: RefreshCcwIcon,
              onClick: handleRefresh,
              disabled: isLoading || loadingStudents,
            },
          ]}
        />

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatCard
            title="Total Sections"
            value={assignedSections.length}
            description="Assigned to you"
            icon={Layers}
            isLoading={isLoading}
          />
          <StatCard
            title="Active Sections"
            value={
              assignedSections.filter((section) => section.isActive !== false)
                .length
            }
            description="Currently teaching"
            icon={GraduationCap}
            isLoading={isLoading}
          />
          <StatCard
            title="Total Students"
            value={
              students
                ? students.filter((student) =>
                    assignedSections.some(
                      (section) =>
                        section._id === student.section?._id ||
                        section._id === student.section
                    )
                  ).length
                : 0
            }
            description="Across all sections"
            icon={Users}
            isLoading={isLoading}
          />
        </div>

        {isLoading ? (
          <Container className="py-8">
            <div className="space-y-6">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="h-32 bg-muted animate-pulse rounded"
                  />
                ))}
              </div>
            </div>
          </Container>
        ) : (
          <>
            {!assignedSections || assignedSections.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Layers className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Sections Assigned
                  </h3>
                  <p className="text-muted-foreground text-center max-w-md">
                    You don't have any sections assigned yet. Please contact
                    your administrator to get sections assigned to your profile.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Section List Sidebar */}
                <div className="lg:col-span-1">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Layers className="h-5 w-5" />
                        My Sections ({assignedSections.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-[400px]">
                        <div className="space-y-2">
                          {assignedSections.map((section) => (
                            <div
                              key={section._id}
                              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                                selectedSection?._id === section._id
                                  ? "bg-primary/5 border-primary"
                                  : "hover:bg-muted/50"
                              }`}
                              onClick={() => handleSectionSelect(section)}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <h4 className="font-medium">
                                    Section {section.name}
                                  </h4>
                                  <p className="text-sm text-muted-foreground">
                                    {section.classId?.name || "N/A"}
                                  </p>
                                </div>
                                <ChevronRight className="h-4 w-4 text-muted-foreground" />
                              </div>
                              <div className="mt-2 flex items-center gap-2">
                                <Badge variant="secondary" className="text-xs">
                                  {section.classId?.academicYear || "N/A"}
                                </Badge>
                                {section.classId?.department && (
                                  <Badge variant="outline" className="text-xs">
                                    {section.classId.department.name}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>

                {/* Section Details */}
                <div className="lg:col-span-2">
                  {selectedSection ? (
                    <div className="space-y-6">
                      {/* Section Header */}
                      <Card>
                        <CardHeader>
                          <div className="flex items-start justify-between">
                            <div>
                              <CardTitle className="text-2xl">
                                {selectedSection.classId?.name ||
                                  "Unknown Class"}
                              </CardTitle>
                              <p className="text-muted-foreground mt-1">
                                Section: {selectedSection.name} | Class Code:{" "}
                                {selectedSection.classId?.code || "N/A"}
                              </p>
                            </div>
                            <Badge
                              variant={
                                selectedSection.isActive !== false
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {selectedSection.isActive !== false
                                ? "Active"
                                : "Inactive"}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <span className="text-muted-foreground">
                                  Academic Year:
                                </span>
                                <span className="font-medium">
                                  {selectedSection.classId?.academicYear ||
                                    "N/A"}
                                </span>
                              </div>
                              {selectedSection.classId?.department && (
                                <div className="flex items-center gap-2 text-sm">
                                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                                  <span className="text-muted-foreground">
                                    Department:
                                  </span>
                                  <span className="font-medium">
                                    {selectedSection.classId.department.name}
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm">
                                <Users className="h-4 w-4 text-muted-foreground" />
                                <span className="text-muted-foreground">
                                  Students:
                                </span>
                                <span className="font-medium">
                                  {sectionStudents.length}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-sm">
                                <Layers className="h-4 w-4 text-muted-foreground" />
                                <span className="text-muted-foreground">
                                  Section:
                                </span>
                                <span className="font-medium">
                                  {selectedSection.name}
                                </span>
                              </div>
                            </div>
                          </div>
                          {selectedSection.description && (
                            <div className="mt-4">
                              <p className="text-sm text-muted-foreground">
                                {selectedSection.description}
                              </p>
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      {/* Section Actions */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Button asChild className="h-auto p-4">
                          <Link
                            to="/dashboard/teacher/attendance"
                            state={{
                              selectedSection: selectedSection,
                              selectedClass: selectedSection.classId,
                              sectionId: selectedSection._id,
                            }}
                          >
                            <div className="flex flex-col items-center gap-2">
                              <UserCheck className="h-6 w-6" />
                              <span>Take Attendance</span>
                            </div>
                          </Link>
                        </Button>
                        <Button
                          asChild
                          variant="outline"
                          className="h-auto p-4"
                        >
                          <Link
                            to="/dashboard/teacher/assignments"
                            state={{
                              selectedSection: selectedSection,
                              selectedClass: selectedSection.classId,
                              sectionId: selectedSection._id,
                            }}
                          >
                            <div className="flex flex-col items-center gap-2">
                              <FileText className="h-6 w-6" />
                              <span>Assignments</span>
                            </div>
                          </Link>
                        </Button>
                        <Button
                          asChild
                          variant="outline"
                          className="h-auto p-4"
                        >
                          <Link
                            to="/dashboard/teacher/timetable"
                            state={{
                              selectedSection: selectedSection,
                              selectedClass: selectedSection.classId,
                              sectionId: selectedSection._id,
                            }}
                          >
                            <div className="flex flex-col items-center gap-2">
                              <Clock className="h-6 w-6" />
                              <span>View Timetable</span>
                            </div>
                          </Link>
                        </Button>
                      </div>

                      {/* Students List */}
                      <Card>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <CardTitle className="flex items-center gap-2">
                              <Users className="h-5 w-5" />
                              Students ({sectionStudents.length})
                            </CardTitle>
                            <div className="relative w-64">
                              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                              <Input
                                placeholder="Search students..."
                                value={studentSearchQuery}
                                onChange={(e) =>
                                  setStudentSearchQuery(e.target.value)
                                }
                                className="pl-8"
                              />
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {loadingStudents ? (
                            <div className="space-y-3">
                              {[1, 2, 3].map((i) => (
                                <div
                                  key={i}
                                  className="h-16 bg-muted animate-pulse rounded"
                                />
                              ))}
                            </div>
                          ) : sectionStudents.length === 0 ? (
                            <div className="text-center py-8">
                              <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                              <h3 className="text-lg font-medium mb-2">
                                {studentSearchQuery
                                  ? "No Students Match Your Search"
                                  : "No Students Found"}
                              </h3>
                              <p className="text-muted-foreground">
                                {studentSearchQuery
                                  ? `No students found matching "${studentSearchQuery}". Try a different search term.`
                                  : "No active students are enrolled in this section."}
                              </p>
                              {studentSearchQuery && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mt-4"
                                  onClick={() => setStudentSearchQuery("")}
                                >
                                  Clear Search
                                </Button>
                              )}
                            </div>
                          ) : (
                            <ScrollArea className="h-[300px]">
                              <div className="space-y-3">
                                {sectionStudents.map((student) => (
                                  <div
                                    key={student._id}
                                    className="flex items-center gap-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                                  >
                                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                                      <span className="text-sm font-medium text-primary">
                                        {student.firstName?.charAt(0)}
                                        {student.lastName?.charAt(0)}
                                      </span>
                                    </div>
                                    <div className="flex-1">
                                      <div className="flex items-center gap-2">
                                        <h4 className="font-medium">
                                          {student.firstName} {student.lastName}
                                        </h4>
                                        <Badge
                                          variant="outline"
                                          className="text-xs"
                                        >
                                          {student.rollNumber}
                                        </Badge>
                                      </div>
                                      <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                                        <span>{student.email}</span>
                                        <span>•</span>
                                        <span>
                                          Class:{" "}
                                          {selectedSection.classId?.name ||
                                            "N/A"}
                                        </span>
                                      </div>
                                    </div>
                                    <Button asChild variant="ghost" size="sm">
                                      <Link
                                        to={`/dashboard/students/${student._id}`}
                                      >
                                        View Profile
                                      </Link>
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </ScrollArea>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  ) : (
                    <Card>
                      <CardContent className="flex flex-col items-center justify-center py-12">
                        <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          Select a Section
                        </h3>
                        <p className="text-muted-foreground text-center">
                          Choose a section from the sidebar to view details and
                          manage students.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}
          </>
        )}
      </main>
    </div>
  );
};

export default AssignedSections;
