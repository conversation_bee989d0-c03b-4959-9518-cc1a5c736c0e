import { Card, CardContent } from "@/components/ui/card";
import { eventTags } from "@/utils/form-options";

export const CalendarLegend = ({ view }) => {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center gap-6 text-sm text-muted-foreground flex-wrap">
            {eventTags.map((item) => (
              <div key={item.type} className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded border ${item.color}`}></div>
                <span>{item.label}</span>
              </div>
            ))}
          </div>
          <div className="text-sm text-muted-foreground">
            {view === "month"
              ? "Click on any date to view events"
              : "Scroll to view all time slots"}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
