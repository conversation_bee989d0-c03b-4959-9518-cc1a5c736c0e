import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Calendar as CalendarDays,
  PlusCircle,
  Calendar,
  Eye,
  Users,
  Clock,
} from "lucide-react";
import { useEvent } from "@/context/event-context";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/dashboard/page-header";
import { formatDate, formatTime } from "@/utils/format-options";
import {
  getEventTypeColor,
  getEventTypeIcon,
  getEventStats,
  getWeekDays,
} from "@/utils/get-functions";
import { StatCard } from "@/components/dashboard/stat-card";
import { CalendarControls } from "@/components/calendar/calendar-component/calendar-controls";
import { CalendarContent } from "@/components/calendar/calendar-component/calendar-content";
import { CalendarSidebar } from "@/components/calendar/calendar-component/calendar-sidebar";
import { CalendarLegend } from "@/components/calendar/calendar-component/calendar-legend";
export default function CalendarPage() {
  const navigate = useNavigate();
  const { events, isLoading, fetchAllEvents } = useEvent();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [filteredEvents, setFilteredEvents] = useState([]);
  const [filterType, setFilterType] = useState("all");
  const [view, setView] = useState("month");
  const [showEventDialog, setShowEventDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);

  useEffect(() => {
    fetchAllEvents();
  }, []);

  useEffect(() => {
    if (events.length > 0 && filteredEvents.length === 0) {
      const firstEvent = events[0];
      if (firstEvent && firstEvent.startDate) {
        const eventDate = new Date(firstEvent.startDate);
        const currentMonth = currentDate.getMonth();
        const currentYear = currentDate.getFullYear();
        const eventMonth = eventDate.getMonth();
        const eventYear = eventDate.getFullYear();

        if (currentMonth !== eventMonth || currentYear !== eventYear) {
          setCurrentDate(new Date(eventYear, eventMonth, 1));
        }
      }
    }
  }, [events, filteredEvents, currentDate]);

  useEffect(() => {
    if (filterType === "all") {
      setFilteredEvents(events);
    } else {
      setFilteredEvents(
        events.filter((event) => event.eventType === filterType)
      );
    }
  }, [filterType, events]);

  const getEventsForDate = (date) => {
    if (!date) return [];
    return filteredEvents.filter((event) => {
      const eventDate = new Date(event.startDate);
      return eventDate.toDateString() === date.toDateString();
    });
  };

  const getUpcomingEvents = () => {
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

    return filteredEvents
      .filter((event) => {
        const eventDate = new Date(event.startDate);
        return eventDate >= today && eventDate <= nextWeek;
      })
      .sort(
        (a, b) =>
          new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
      )
      .slice(0, 5);
  };

  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setShowEventDialog(true);
  };

  const isSelected = (date) => {
    if (!date) return false;
    return date.toDateString() === selectedDate.toDateString();
  };

  const getViewTitle = () => {
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];

    if (view === "month") {
      return `${
        monthNames[currentDate.getMonth()]
      } ${currentDate.getFullYear()}`;
    } else if (view === "week") {
      const weekDays = getWeekDays(currentDate);
      const startDate = weekDays[0];
      const endDate = weekDays[6];
      if (startDate.getMonth() === endDate.getMonth()) {
        return `${
          monthNames[startDate.getMonth()]
        } ${startDate.getDate()} - ${endDate.getDate()}, ${startDate.getFullYear()}`;
      } else {
        return `${monthNames[startDate.getMonth()]} ${startDate.getDate()} - ${
          monthNames[endDate.getMonth()]
        } ${endDate.getDate()}, ${startDate.getFullYear()}`;
      }
    } else {
      return `${
        monthNames[currentDate.getMonth()]
      } ${currentDate.getDate()}, ${currentDate.getFullYear()}`;
    }
  };

  const stats = getEventStats(filteredEvents);

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="School Calendar"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Schedule", href: "/dashboard/schedules" },
            { label: "Calendar" },
          ]}
          actions={[
            {
              label: "Create Event",
              icon: PlusCircle,
              href: "/dashboard/schedules/events/create",
            },
          ]}
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Events"
            value={stats.total}
            description="All events"
            icon={Calendar}
            isLoading={isLoading}
            trend="positive"
          />
          <StatCard
            title="This Month"
            value={stats.thisMonth}
            description="Events this month"
            icon={CalendarDays}
            isLoading={isLoading}
          />
          <StatCard
            title="Classes"
            value={stats.classes}
            description="Scheduled classes"
            icon={Users}
            isLoading={isLoading}
          />
          <StatCard
            title="Meetings"
            value={stats.meetings}
            description="Scheduled meetings"
            icon={Clock}
            isLoading={isLoading}
          />
        </div>

        {/* Calendar Grid and Sidebar */}
        <div className="grid grid-cols-1 gap-6">
          {/* Calendar Grid */}
          <Card>
            <CalendarControls
              view={view}
              setView={setView}
              filterType={filterType}
              setFilterType={setFilterType}
              getViewTitle={getViewTitle}
              setCurrentDate={setCurrentDate}
              setSelectedDate={setSelectedDate}
            />
            <CalendarContent
              view={view}
              isLoading={isLoading}
              isSelected={isSelected}
              currentDate={currentDate}
              setSelectedDate={setSelectedDate}
              onEventClick={handleEventClick}
              getEventsForDate={getEventsForDate}
            />
          </Card>

          <CalendarSidebar
            filterType={filterType}
            setFilterType={setFilterType}
            getUpcomingEvents={getUpcomingEvents}
            onEventClick={handleEventClick}
          />
        </div>

        {/* Legend */}
        <CalendarLegend view={view} />

        {/* Event Detail Dialog */}
        <Dialog open={showEventDialog} onOpenChange={setShowEventDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <span className="text-2xl">
                  {selectedEvent && getEventTypeIcon(selectedEvent.eventType)}
                </span>
                {selectedEvent?.title}
              </DialogTitle>
            </DialogHeader>
            {selectedEvent && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Date
                    </label>
                    <p className="text-sm">
                      {formatDate(selectedEvent.startDate)}
                      {selectedEvent.startDate !== selectedEvent.endDate &&
                        ` - ${formatDate(selectedEvent.endDate)}`}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Time
                    </label>
                    <p className="text-sm">
                      {selectedEvent.isAllDayEvent
                        ? "All Day"
                        : `${formatTime(
                            selectedEvent.startTime
                          )} - ${formatTime(selectedEvent.endTime)}`}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Type
                    </label>
                    <Badge
                      className={getEventTypeColor(selectedEvent.eventType)}
                    >
                      {selectedEvent.eventType}
                    </Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Status
                    </label>
                    <Badge variant="outline">{selectedEvent.status}</Badge>
                  </div>
                </div>

                {selectedEvent.description && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Description
                    </label>
                    <p className="text-sm mt-1">{selectedEvent.description}</p>
                  </div>
                )}

                {selectedEvent.location && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Location
                    </label>
                    <p className="text-sm mt-1">{selectedEvent.location}</p>
                  </div>
                )}

                <div className="flex gap-2 pt-4">
                  <Button
                    size="sm"
                    onClick={() => {
                      navigate(
                        `/dashboard/schedules/events/${
                          selectedEvent._id || selectedEvent.id
                        }`
                      );
                    }}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowEventDialog(false)}
                  >
                    Close
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </main>
    </div>
  );
}
