import { formatTime } from "@/utils/format-options";
import {
  getEventTypeColor,
  getTimeSlots,
  getEventPosition,
} from "@/utils/get-functions";
import { MapPin } from "lucide-react";

export const DayView = ({ currentDate, onEventClick, getEventsForDate }) => {
  const timeSlots = getTimeSlots();
  const dayEvents = getEventsForDate(currentDate);
  const weekDays = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const getEventBackgroundColor = (eventType) => {
    const colorClass = getEventTypeColor(eventType);
    if (colorClass.includes("red")) return "#fef2f2";
    if (colorClass.includes("blue")) return "#eff6ff";
    if (colorClass.includes("green")) return "#f0fdf4";
    if (colorClass.includes("purple")) return "#faf5ff";
    if (colorClass.includes("orange")) return "#fffbeb";
    if (colorClass.includes("yellow")) return "#fefce8";
    if (colorClass.includes("pink")) return "#fdf2f8";
    return "#f0f9ff";
  };

  const getEventBorderColor = (eventType) => {
    const colorClass = getEventTypeColor(eventType);
    if (colorClass.includes("red")) return "#ef4444";
    if (colorClass.includes("blue")) return "#3b82f6";
    if (colorClass.includes("green")) return "#10b981";
    if (colorClass.includes("purple")) return "#8b5cf6";
    if (colorClass.includes("orange")) return "#f59e0b";
    if (colorClass.includes("yellow")) return "#eab308";
    if (colorClass.includes("pink")) return "#ec4899";
    return "#6366f1";
  };

  return (
    <div className="space-y-4">
      {/* Day Header */}
      <div className="text-center p-4 border rounded-lg bg-muted/30">
        <div className="text-2xl font-bold">{currentDate.getDate()}</div>
        <div className="text-sm text-muted-foreground">
          {weekDays[currentDate.getDay()]}, {monthNames[currentDate.getMonth()]}{" "}
          {currentDate.getFullYear()}
        </div>
      </div>

      {/* Day Schedule */}
      <div className="grid grid-cols-[120px_1fr] gap-4">
        {/* Time Column */}
        <div className="space-y-0">
          {timeSlots.map((time) => (
            <div
              key={time}
              className="h-16 flex items-start justify-end pr-2 text-sm text-muted-foreground"
            >
              {formatTime(time)}
            </div>
          ))}
        </div>

        {/* Events Column */}
        <div className="relative border-l border-border">
          {/* Time Grid Lines */}
          {timeSlots.map((time) => (
            <div key={time} className="h-16 border-b border-border/50"></div>
          ))}

          {/* Events */}
          {dayEvents.map((event) => {
            const position = getEventPosition(event);
            return (
              <div
                key={event._id}
                className="absolute left-2 right-2 rounded-lg p-3 shadow-sm cursor-pointer hover:shadow-md transition-shadow z-10"
                style={{
                  backgroundColor: getEventBackgroundColor(event.eventType),
                  borderLeft: `4px solid ${getEventBorderColor(
                    event.eventType
                  )}`,
                  top: position.top,
                  height: position.height,
                  minHeight: "60px",
                }}
                onClick={() => onEventClick(event)}
              >
                <div className="font-medium text-sm text-foreground">
                  {event.title}
                </div>
                {event.startTime && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {formatTime(event.startTime)}
                    {event.endTime && ` - ${formatTime(event.endTime)}`}
                  </div>
                )}
                {event.venueName && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                    <MapPin className="h-3 w-3" />
                    {event.venueName}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
