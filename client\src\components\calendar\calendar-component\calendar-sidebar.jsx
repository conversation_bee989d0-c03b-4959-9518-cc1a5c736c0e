import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Clock,
  MapPin,
  Filter,
  TrendingUp,
  Calendar as CalendarDays,
} from "lucide-react";
import { getEventTypeColor, getEventTypeIcon } from "@/utils/get-functions";
import { formatTime } from "@/utils/format-options";
import { cn } from "@/lib/utils";

export const CalendarSidebar = ({
  filterType,
  setFilterType,
  getUpcomingEvents,
  onEventClick,
}) => {
  const eventFilters = [
    { key: "all", label: "All Events", color: "#6B7280" },
    { key: "exam", label: "Exams", color: "#EF4444" },
    { key: "meeting", label: "Meetings", color: "#3B82F6" },
    { key: "workshop", label: "Workshops", color: "#10B981" },
    { key: "conference", label: "Conferences", color: "#8B5CF6" },
    { key: "seminar", label: "Seminars", color: "#F59E0B" },
    { key: "sports", label: "Sports", color: "#EAB308" },
    { key: "cultural", label: "Cultural", color: "#EC4899" },
    { key: "academic", label: "Academic", color: "#6366F1" },
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-1 gap-6">
      {/* Upcoming Events */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <TrendingUp className="h-4 w-4" />
            Upcoming Events
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {getUpcomingEvents().length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CalendarDays className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No upcoming events</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-[400px] overflow-y-auto pr-1">
              {getUpcomingEvents().map((event) => (
                <Card
                  key={event._id}
                  className={cn(
                    "transition-all duration-200 hover:shadow-md cursor-pointer border-l-5",
                    event.priority === "urgent" &&
                      "border-l-primary bg-primary/10"
                  )}
                  style={{
                    borderLeftColor: getEventTypeColor(event.eventType),
                  }}
                  onClick={() => onEventClick(event)}
                >
                  <CardHeader className="pb-0 flex flex-row items-start gap-3">
                    <div
                      className={`p-1.5 rounded-md shrink-0 ${getEventTypeColor(
                        event.eventType
                      )}`}
                    >
                      {getEventTypeIcon(event.eventType)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-sm font-medium leading-tight line-clamp-2">
                        {event.title}
                      </CardTitle>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                        <Clock className="h-3 w-3 shrink-0" />
                        <span className="truncate">
                          {event.startTime && event.endTime
                            ? `${formatTime(event.startTime)} - ${formatTime(
                                event.endTime
                              )}`
                            : "All Day"}
                        </span>
                      </div>
                      {event.venueName && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                          <MapPin className="h-3 w-3 shrink-0" />
                          <span className="truncate">{event.venueName}</span>
                        </div>
                      )}
                    </div>
                  </CardHeader>

                  <CardFooter className="px-3 flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {new Date(event.startDate).toLocaleDateString()}
                    </Badge>
                    <Badge
                      variant="secondary"
                      className={`text-xs capitalize ${getEventTypeColor(
                        event.eventType
                      )}`}
                    >
                      {event.eventType}
                    </Badge>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Event Filters */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Filter className="h-4 w-4" />
            Filter Events
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-2">
            {eventFilters.map((filter) => (
              <Button
                key={filter.key}
                variant={filterType === filter.key ? "default" : "ghost"}
                className="w-full justify-start gap-3 h-9 px-3"
                onClick={() => setFilterType(filter.key)}
              >
                <div
                  className="w-3 h-3 rounded-full shrink-0"
                  style={{ backgroundColor: filter.color }}
                />
                <span className="truncate">{filter.label}</span>
                {filterType === filter.key && (
                  <div className="ml-auto w-2 h-2 rounded-full bg-current" />
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
