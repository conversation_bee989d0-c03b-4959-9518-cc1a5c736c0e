import { parseISO, format } from "date-fns";

export function formatTime(time) {
  // Handle null, undefined, or non-string values
  if (!time || typeof time !== "string") {
    return "Invalid time";
  }

  // Check if time contains ":"
  if (!time.includes(":")) {
    return time; // Return as-is if it's not in HH:MM format
  }

  try {
    const [hours, minutes] = time.split(":");
    const hour = Number.parseInt(hours);
    const ampm = hour >= 12 ? "PM" : "AM";
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  } catch (error) {
    console.error("Error formatting time:", error);
    return "Invalid time";
  }
}

export const formatDate = (date) => {
  if (!date) return "Unknown";
  try {
    const parsedDate = typeof date === "string" ? parseISO(date) : date;
    return format(parsedDate, "PPP");
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Invalid date";
  }
};

export const formatTimeAgo = (date) => {
  if (!date) return "Unknown";
  const now = new Date();
  const itemDate = new Date(date);
  const diffInHours = Math.floor((now - itemDate) / (1000 * 60 * 60));

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now - itemDate) / (1000 * 60));
    return diffInMinutes <= 1 ? "Just now" : `${diffInMinutes} minutes ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
  } else {
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
  }
};
