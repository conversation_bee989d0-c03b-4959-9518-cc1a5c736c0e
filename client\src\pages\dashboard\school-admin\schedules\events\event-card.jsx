import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON>ert<PERSON><PERSON>og,
  <PERSON>ertD<PERSON>og<PERSON>ontent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { formatTimeAgo } from "@/utils/date-filters";
import { StatusBadge } from "@/components/data-card/data-card-component/status-badge";
import { DropdownActions } from "@/components/data-card/data-card-component/dropdown-actions";
import { Clock, MapPin } from "lucide-react";
import { getEventTypeIcon } from "@/utils/get-functions";
import { formatDate, formatTime } from "@/utils/format-options";

export const EventCard = ({
  event,
  onEdit,
  onView,
  onDelete,
  onStatusChange,
}) => {
  const [openDelete, setOpenDelete] = useState(false);
  const [openStatusChange, setOpenStatusChange] = useState(false);

  const handleDelete = async () => {
    try {
      await onDelete(event._id);
      toast.success(`${event.title} deleted successfully.`);
      setOpenDelete(false);
    } catch (error) {
      console.error("Failed to delete event:", error);
      toast.error("Failed to delete event. Please try again.");
    }
  };

  const handleStatusUpdate = async (newStatus) => {
    try {
      await onStatusChange(event._id, newStatus);
      toast.success(`Event status updated to ${newStatus.replace("_", " ")}`);
      setOpenStatusChange(false);
    } catch (error) {
      console.error("Failed to update event status:", error);
      toast.error("Failed to update event status. Please try again.");
    }
  };

  return (
    <>
      <Card
        className={cn(
          "transition-all duration-200 hover:shadow-md cursor-pointer border-l-4 border-l-primary bg-muted/20",
          event.priority === "urgent" &&
            "border-l-4 border-l-primary bg-accent/30"
        )}
      >
        <CardContent className="px-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <div className="flex-shrink-0 mt-1">
                <div className="text-lg">
                  {getEventTypeIcon(event.eventType)}
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="text-sl font-semibold text-foreground line-clamp-1">
                    {event.title}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <StatusBadge
                      row={{ original: event }}
                      statusField="priority"
                      variant={{
                        low: "success",
                        urgent: "destructive",
                        high: "destructive",
                        medium: "warning",
                      }}
                    />
                    <StatusBadge
                      row={{ original: event }}
                      showText={false}
                      statusField="status"
                      variant={{
                        scheduled: "warning",
                        in_progress: "default",
                        completed: "success",
                        cancelled: "destructive",
                        postponed: "secondary",
                      }}
                    />
                  </div>
                </div>
                <div className="flex items-center text-xs text-muted-foreground mb-2">
                  <span className="font-medium capitalize">
                    {event.eventType.replace("_", " ")}
                  </span>
                  <span className="mx-1">•</span>
                  <span className="capitalize">
                    {event.attendeeType?.replace("_", " ") || "All"}
                  </span>
                  <span className="mx-1">•</span>
                  <span>{formatDate(event.startDate)}</span>
                </div>
                <p className="text-sm text-foreground/80 line-clamp-2 mb-2">
                  {event.description}
                </p>
                <div className="flex items-center text-xs text-muted-foreground mb-2">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span className="truncate mr-3">{event.venueName}</span>
                  {!event.isAllDayEvent && event.startTime && (
                    <>
                      <Clock className="h-3 w-3 mr-1" />
                      <span>{formatTime(event.startTime)}</span>
                    </>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">
                    {formatTimeAgo(event.createdAt)}
                  </span>
                  <button
                    onClick={() => onView(event._id)}
                    className="text-xs text-primary hover:text-primary/80 font-medium"
                  >
                    View Details
                  </button>
                </div>
              </div>
            </div>
            <DropdownActions
              item={event}
              onView={onView}
              onEdit={onEdit}
              onStatusChange={() => setOpenStatusChange(true)}
              onDelete={() => setOpenDelete(true)}
              setOpenDelete={setOpenDelete}
              statusKey="status"
              completedStatus="completed"
              scheduledStatus="scheduled"
            />
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {event.title}?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this event? This action cannot be
              undone.
              {event.status === "completed" && (
                <span className="block mt-2 text-amber-600 font-medium">
                  Note: This event has already been completed.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={openStatusChange} onOpenChange={setOpenStatusChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Update Event Status</AlertDialogTitle>
            <AlertDialogDescription>
              Update the status of "{event.title}". This will change how the
              event is displayed and tracked.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleStatusUpdate("in_progress")}
            >
              Mark In Progress
            </AlertDialogAction>
            <AlertDialogAction onClick={() => handleStatusUpdate("completed")}>
              Mark Complete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
