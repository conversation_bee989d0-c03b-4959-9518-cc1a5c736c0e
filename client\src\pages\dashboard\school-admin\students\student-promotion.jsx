import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>eader } from "@/components/dashboard/page-header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  <PERSON>,
  <PERSON>R<PERSON>,
  Grad<PERSON><PERSON>ap,
  <PERSON>Circle,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ren<PERSON>U<PERSON>,
} from "lucide-react";
import { useStudent } from "@/context/student-context";
import { useClass } from "@/context/class-context";
import { useSection } from "@/context/section-context";
import { toast } from "sonner";

const StudentPromotion = () => {
  const { students, fetchAllStudents, promoteStudents } = useStudent();
  const { classes, fetchAllClasses, classOptions } = useClass();
  const { sections, fetchAllSections, fetchSectionsByClass, sectionOptions } =
    useSection();

  const [currentClass, setCurrentClass] = useState("");
  const [currentSection, setCurrentSection] = useState("");
  const [targetClass, setTargetClass] = useState("");
  const [targetSection, setTargetSection] = useState("");

  const [filteredStudents, setFilteredStudents] = useState([]);
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [targetSections, setTargetSections] = useState([]);

  useEffect(() => {
    fetchAllStudents();
    fetchAllClasses();
    fetchAllSections();
  }, []);

  useEffect(() => {
    let filtered = students.filter((student) => student.status === "active");

    if (currentClass && currentClass !== "all") {
      filtered = filtered.filter(
        (student) =>
          student.class?._id === currentClass || student.class === currentClass
      );
    }

    if (currentSection && currentSection !== "all") {
      filtered = filtered.filter(
        (student) =>
          student.section?._id === currentSection ||
          student.section === currentSection
      );
    }

    setFilteredStudents(filtered);
    setSelectedStudents([]);
    setSelectAll(false);
  }, [students, currentClass, currentSection]);

  useEffect(() => {
    if (targetClass) {
      fetchSectionsByClass(targetClass)
        .then((sectionsData) => {
          setTargetSections(sectionsData || []);
        })
        .catch(() => {
          setTargetSections([]);
        });
    } else {
      setTargetSections([]);
    }
    setTargetSection("");
  }, [targetClass]);

  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedStudents(filteredStudents.map((student) => student._id));
    } else {
      setSelectedStudents([]);
    }
  };

  const handleStudentSelect = (studentId, checked) => {
    if (checked) {
      setSelectedStudents((prev) => [...prev, studentId]);
    } else {
      setSelectedStudents((prev) => prev.filter((id) => id !== studentId));
      setSelectAll(false);
    }
  };

  const handlePromoteStudents = async () => {
    if (!targetClass || !targetSection) {
      toast.error("Please select target class and section");
      return;
    }

    if (selectedStudents.length === 0) {
      toast.error("Please select at least one student to promote");
      return;
    }

    setIsLoading(true);
    try {
      await promoteStudents(selectedStudents, targetClass, targetSection);

      toast.success("Students promoted successfully", {
        description: `${selectedStudents.length} student(s) have been promoted to the new class.`,
      });
      setSelectedStudents([]);
      setSelectAll(false);
      setTargetClass("");
      setTargetSection("");
      fetchAllStudents();
    } catch (error) {
      console.error("Error promoting students:", error);
      toast.error("Failed to promote students", {
        description:
          "Please try again or contact support if the issue persists.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getSelectedStudentsData = () => {
    return filteredStudents.filter((student) =>
      selectedStudents.includes(student._id)
    );
  };

  const getCurrentClassName = () => {
    if (!currentClass || currentClass === "all") return "All Classes";
    const classData = classes.find((cls) => cls._id === currentClass);
    return classData ? `${classData.name} (${classData.code})` : "All Classes";
  };

  const getCurrentSectionName = () => {
    if (!currentSection || currentSection === "all") return "All Sections";
    const sectionData = sections.find((sec) => sec._id === currentSection);
    return sectionData
      ? `${sectionData.name} (${sectionData.code})`
      : "All Sections";
  };

  const getTargetClassName = () => {
    const classData = classes.find((cls) => cls._id === targetClass);
    return classData ? `${classData.name} (${classData.code})` : "";
  };

  const getTargetSectionName = () => {
    const sectionData = targetSections.find((sec) => sec._id === targetSection);
    return sectionData ? `${sectionData.name} (${sectionData.code})` : "";
  };

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          title="Student Promotion"
          description="Promote students from one class to another"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Students", href: "/dashboard/students" },
            { label: "Promotion" },
          ]}
          actions={[
            {
              icon: Users,
              label: "Add Students",
              href: "/dashboard/students/create",
            },
          ]}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Filter Panel */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filter Students
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Current Class
                  </label>
                  <Select value={currentClass} onValueChange={setCurrentClass}>
                    <SelectTrigger className="w-[220px]">
                      <SelectValue placeholder="Select current class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Classes</SelectItem>
                      {classOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Current Section
                  </label>
                  <Select
                    value={currentSection}
                    onValueChange={setCurrentSection}
                  >
                    <SelectTrigger className="w-[220px]">
                      <SelectValue placeholder="Select current section" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Sections</SelectItem>
                      {sectionOptions
                        .filter(
                          (option) =>
                            !currentClass ||
                            currentClass === "all" ||
                            option.classId === currentClass
                        )
                        .map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Target Class
                  </label>
                  <Select value={targetClass} onValueChange={setTargetClass}>
                    <SelectTrigger className="w-[220px]">
                      <SelectValue placeholder="Select target class" />
                    </SelectTrigger>
                    <SelectContent>
                      {classOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Target Section
                  </label>
                  <Select
                    value={targetSection}
                    onValueChange={setTargetSection}
                    disabled={!targetClass}
                  >
                    <SelectTrigger className="w-[220px]">
                      <SelectValue placeholder="Select target section" />
                    </SelectTrigger>
                    <SelectContent>
                      {targetSections.map((section) => (
                        <SelectItem key={section._id} value={section._id}>
                          {section.name} ({section.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedStudents.length > 0 && (
                  <Alert>
                    <UserCheck className="h-4 w-4" />
                    <AlertDescription>
                      {selectedStudents.length} student(s) selected for
                      promotion
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Student List */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Students ({filteredStudents.length})
                  </CardTitle>
                  {filteredStudents.length > 0 && (
                    <div className="flex items-center gap-2">
                      <Checkbox
                        id="select-all"
                        checked={selectAll}
                        onCheckedChange={handleSelectAll}
                      />
                      <label
                        htmlFor="select-all"
                        className="text-sm font-medium"
                      >
                        Select All
                      </label>
                    </div>
                  )}
                </div>
                {(currentClass && currentClass !== "all") ||
                (currentSection && currentSection !== "all") ? (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>Showing students from:</span>
                    <Badge variant="secondary">{getCurrentClassName()}</Badge>
                    {currentSection && currentSection !== "all" && (
                      <>
                        <span>-</span>
                        <Badge variant="secondary">
                          {getCurrentSectionName()}
                        </Badge>
                      </>
                    )}
                  </div>
                ) : null}
              </CardHeader>
              <CardContent>
                {filteredStudents.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">
                      No Students Found
                    </h3>
                    <p className="text-muted-foreground">
                      {(currentClass && currentClass !== "all") ||
                      (currentSection && currentSection !== "all")
                        ? "No active students found in the selected class/section."
                        : "Please select a class to view students."}
                    </p>
                  </div>
                ) : (
                  <ScrollArea className="h-[200px]">
                    <div className="space-y-2">
                      {filteredStudents.map((student) => (
                        <div
                          key={student._id}
                          className={`flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                            selectedStudents.includes(student._id)
                              ? "bg-primary/5 border-primary"
                              : "hover:bg-muted/50"
                          }`}
                        >
                          <Checkbox
                            id={`student-${student._id}`}
                            checked={selectedStudents.includes(student._id)}
                            onCheckedChange={(checked) =>
                              handleStudentSelect(student._id, checked)
                            }
                          />
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">
                                {student.firstName} {student.lastName}
                              </h4>
                              <Badge variant="outline" className="text-xs">
                                {student.rollNumber}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                              <span>{student.email}</span>
                              <span>•</span>
                              <span>
                                {typeof student.class === "object"
                                  ? student.class?.name
                                  : student.class || "No Class"}
                              </span>
                              <span>•</span>
                              <span>
                                {typeof student.section === "object"
                                  ? student.section?.name
                                  : student.section || "No Section"}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>

            {/* Promotion Preview */}
            {selectedStudents.length > 0 && targetClass && targetSection && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Promotion Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-center gap-4 p-4 bg-muted/50 rounded-lg">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground mb-1">
                          From
                        </div>
                        <Badge variant="secondary" className="text-sm">
                          {getCurrentClassName()}
                        </Badge>
                        {currentSection && currentSection !== "all" && (
                          <Badge variant="secondary" className="text-sm ml-1">
                            {getCurrentSectionName()}
                          </Badge>
                        )}
                      </div>
                      <ArrowRight className="h-6 w-6 text-primary" />
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground mb-1">
                          To
                        </div>
                        <Badge variant="default" className="text-sm">
                          {getTargetClassName()}
                        </Badge>
                        <Badge variant="default" className="text-sm ml-1">
                          {getTargetSectionName()}
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">
                        Selected Students ({selectedStudents.length})
                      </h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                        {getSelectedStudentsData().map((student) => (
                          <div
                            key={student._id}
                            className="flex items-center gap-2 p-2 bg-background border rounded text-sm"
                          >
                            <CheckCircle className="h-4 w-4 text-success" />
                            <span>
                              {student.firstName} {student.lastName}
                            </span>
                            <Badge
                              variant="outline"
                              className="text-xs ml-auto"
                            >
                              {student.rollNumber}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        This action will move {selectedStudents.length}{" "}
                        student(s) to the selected class and section. This
                        action cannot be undone easily.
                      </AlertDescription>
                    </Alert>

                    <div className="flex gap-2 pt-4">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button className="flex-1" disabled={isLoading}>
                            <GraduationCap className="h-4 w-4 mr-2" />
                            Promote Students
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              Confirm Student Promotion
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to promote{" "}
                              {selectedStudents.length} student(s) from{" "}
                              <strong>{getCurrentClassName()}</strong> to{" "}
                              <strong>{getTargetClassName()}</strong>?
                              <br />
                              <br />
                              This action will update their class and section
                              assignments.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handlePromoteStudents}
                              disabled={isLoading}
                            >
                              {isLoading ? "Promoting..." : "Confirm Promotion"}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSelectedStudents([]);
                          setSelectAll(false);
                        }}
                      >
                        Clear Selection
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default StudentPromotion;
