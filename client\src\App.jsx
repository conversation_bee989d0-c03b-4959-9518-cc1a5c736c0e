import { create<PERSON><PERSON><PERSON><PERSON><PERSON>er, Router<PERSON>rovider } from "react-router-dom";
import { Toaster } from "sonner";

// Layout
import HomeLayout from "@/components/layout/home-layout";
import DashboardLayout from "@/components/layout/dashboard-layout";

// Pages
import Home from "@/pages/Home";
import ContactUs from "@/pages/ContactUs";
import NotFoundPage from "@/pages/NotFoundPage";

// Auth
import SignUp from "@/pages/auth/SignUp";
import SignIn from "@/pages/auth/SignIn";

// Auth Protection
import { ProtectedRoute } from "@/components/auth/protected-route";

// Role-based overview page
import RoleBasedOverview from "@/components/dashboard/role-based-overview";

// Admin
// Contact Directory
import ContactDirectory from "@/pages/dashboard/admin/contact/contact-directory";

// Schools
import CreateSchools from "@/pages/dashboard/admin/schools/create-schools";
import SchoolDirectory from "@/pages/dashboard/admin/schools/school-directory";

// School Admin
import CreateSchoolAdmin from "@/pages/dashboard/admin/school-admin/create-school-admin";
import SchoolAdminDirectory from "@/pages/dashboard/admin/school-admin/school-admin-directory";

// Settings
import Settings from "@/pages/dashboard/Settings";

// Notifications
import CreateNotification from "@/pages/dashboard/notifications/create-notification";
import NotificationDirectory from "@/pages/dashboard/notifications/notification-directory";
import ViewNotification from "./pages/dashboard/notifications/view-notification";

// School Admin

// Students
import CreateStudents from "@/pages/dashboard/school-admin/students/create-students";
import StudentDirectory from "@/pages/dashboard/school-admin/students/student-directory";
import ViewStudent from "@/pages/dashboard/school-admin/students/view-student";
import StudentPromotion from "@/pages/dashboard/school-admin/students/student-promotion";

// Parents
import CreateParents from "@/pages/dashboard/school-admin/parents/create-parents";
import ParentDirectory from "@/pages/dashboard/school-admin/parents/parent-directory";
import ViewParent from "@/pages/dashboard/school-admin/parents/view-parent";

// Teachers
import CreateTeacher from "@/pages/dashboard/school-admin/teachers/create-teacher";
import TeacherDirectory from "@/pages/dashboard/school-admin/teachers/teacher-directory";
import ViewTeacher from "@/pages/dashboard/school-admin/teachers/view-teacher";

// Departments
import CreateDepartments from "@/pages/dashboard/school-admin/academics/departments/create-departments";
import DepartmentManagement from "@/pages/dashboard/school-admin/academics/departments/department-management";

// Subjects
import CreateSubjects from "@/pages/dashboard/school-admin/academics/subjects/create-subject";
import SubjectManagement from "@/pages/dashboard/school-admin/academics/subjects/subject-management";

// Terms
import CreateTerms from "@/pages/dashboard/school-admin/academics/terms/create-terms";
import TermManagement from "@/pages/dashboard/school-admin/academics/terms/term-management";

// Classes and Sections
import CreateClasses from "@/pages/dashboard/school-admin/academics/class-sections/classes/create-classes";
import CreateSections from "@/pages/dashboard/school-admin/academics/class-sections/sections/create-sections";
import ClassSectionManagement from "@/pages/dashboard/school-admin/academics/class-sections/class-section-management";

// Finances
// Fees
import CreateFees from "@/pages/dashboard/school-admin/finances/fees/create-fees";
import FeeDirectory from "@/pages/dashboard/school-admin/finances/fees/fee-directory";
import ViewFee from "@/pages/dashboard/school-admin/finances/fees/view-fee";

// Schedules
// Events
import CreateEvent from "@/pages/dashboard/school-admin/schedules/events/create-event";
import EventDirectory from "@/pages/dashboard/school-admin/schedules/events/event-directory";
import ViewEvent from "@/pages/dashboard/school-admin/schedules/events/view-event";

// Timetable
import TimetableDirectory from "@/pages/dashboard/school-admin/schedules/timetable/timetable-directory";
import CreateTimetable from "@/pages/dashboard/school-admin/schedules/timetable/create-timetable";

// Calendar
import CalendarDirectory from "@/pages/dashboard/school-admin/schedules/calendar/calendar";

// Teacher Dashboard
// Classes
import MyClasses from "@/pages/dashboard/teacher/classes/my-classes";
import AssignedSections from "@/pages/dashboard/teacher/classes/assigned-sections";
// Timetable
import TeacherTimetable from "@/pages/dashboard/teacher/schedules/teacher-timetable";

const router = createBrowserRouter([
  {
    path: "/",
    element: <HomeLayout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: "/contact-us",
        element: <ContactUs />,
      },
    ],
  },
  {
    path: "/sign-up",
    element: <SignUp />,
  },
  {
    path: "/sign-in",
    element: <SignIn />,
  },
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <DashboardLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <RoleBasedOverview />,
      },

      // ===== ADMIN ROUTES =====
      {
        path: "schools",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <SchoolDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "schools/create",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <CreateSchools />
          </ProtectedRoute>
        ),
      },
      {
        path: "schools/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <h1>View School Details</h1>
          </ProtectedRoute>
        ),
      },
      {
        path: "schools/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <CreateSchools />
          </ProtectedRoute>
        ),
      },
      {
        path: "school-admins",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <SchoolAdminDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "school-admins/create",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <CreateSchoolAdmin />
          </ProtectedRoute>
        ),
      },
      {
        path: "school-admins/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <div>View School Admin</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "school-admins/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <CreateSchoolAdmin />
          </ProtectedRoute>
        ),
      },
      {
        path: "contacts",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <ContactDirectory />
          </ProtectedRoute>
        ),
      },

      // ===== SCHOOL ADMIN ROUTES =====
      {
        path: "teachers",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <TeacherDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "teachers/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateTeacher />
          </ProtectedRoute>
        ),
      },
      {
        path: "teachers/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <ViewTeacher />
          </ProtectedRoute>
        ),
      },
      {
        path: "teachers/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateTeacher />
          </ProtectedRoute>
        ),
      },
      {
        path: "teachers/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateTeacher />
          </ProtectedRoute>
        ),
      },
      {
        path: "teachers/attendance",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Teacher Attendance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teachers/performance",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Teacher Performance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "students",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <StudentDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "students/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <ViewStudent />
          </ProtectedRoute>
        ),
      },
      {
        path: "students/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateStudents />
          </ProtectedRoute>
        ),
      },
      {
        path: "students/promotion",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <StudentPromotion />
          </ProtectedRoute>
        ),
      },
      {
        path: "students/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateStudents />
          </ProtectedRoute>
        ),
      },
      {
        path: "students/attendance",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <div className="p-8">Student Attendance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "students/performance",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <div className="p-8">Student Performance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parents",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <ParentDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "parents/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateParents />
          </ProtectedRoute>
        ),
      },
      {
        path: "parents/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <ViewParent />
          </ProtectedRoute>
        ),
      },
      {
        path: "parents/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateParents />
          </ProtectedRoute>
        ),
      },
      {
        path: "parents/communication",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <div className="p-8">Parent Communication</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/classes",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <ClassSectionManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/classes/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateClasses />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/classes/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateClasses />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/sections/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateSections />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/sections/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateSections />
          </ProtectedRoute>
        ),
      },

      {
        path: "academics/departments",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <DepartmentManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/departments/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateDepartments />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/departments/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateDepartments />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/subjects",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <SubjectManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/subjects/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateSubjects />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/subjects/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateSubjects />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/terms",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <TermManagement />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/terms/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateTerms />
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/terms/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateTerms />
          </ProtectedRoute>
        ),
      },
      {
        path: "finances/fees",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <FeeDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "finances/fees/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <ViewFee />
          </ProtectedRoute>
        ),
      },
      {
        path: "finances/fees/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateFees />
          </ProtectedRoute>
        ),
      },
      {
        path: "finances/fees/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateFees />
          </ProtectedRoute>
        ),
      },
      {
        path: "schedules/calendar",
        element: (
          <ProtectedRoute
            allowedRoles={[
              "admin",
              "school-admin",
              "teacher",
              "student",
              "parent",
            ]}
          >
            <CalendarDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "schedules/events",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <EventDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "schedules/events/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateEvent />
          </ProtectedRoute>
        ),
      },
      {
        path: "schedules/events/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <ViewEvent />
          </ProtectedRoute>
        ),
      },
      {
        path: "schedules/events/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateEvent />
          </ProtectedRoute>
        ),
      },
      {
        path: "schedules/timetable",
        element: (
          <ProtectedRoute
            allowedRoles={["admin", "school-admin", "teacher", "student"]}
          >
            <TimetableDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "schedules/timetable/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateTimetable />
          </ProtectedRoute>
        ),
      },
      {
        path: "schedules/timetable/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateTimetable />
          </ProtectedRoute>
        ),
      },

      // ===== TEACHER ROUTES =====
      {
        path: "teacher/classes",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <MyClasses />
          </ProtectedRoute>
        ),
      },
      {
        path: "teacher/sections",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <AssignedSections />
          </ProtectedRoute>
        ),
      },
      {
        path: "teacher/attendance",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <div className="p-8">Take Attendance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teacher/assignments",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <div className="p-8">Assignments</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teacher/timetable",
        element: (
          <ProtectedRoute>
            <TeacherTimetable />
          </ProtectedRoute>
        ),
      },
      {
        path: "teacher/calendar",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <div className="p-8">My Calendar</div>
          </ProtectedRoute>
        ),
      },

      // ===== STUDENT ROUTES =====
      {
        path: "student/classes",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Classes</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "student/assignments",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Assignments</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "student/grades",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Grades</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "student/timetable",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Timetable</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "student/calendar",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Calendar</div>
          </ProtectedRoute>
        ),
      },

      // ===== PARENT ROUTES =====
      {
        path: "parent/childrens",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">My Children</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parent/childrens/performance",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">Children Performance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parent/childrens/attendance",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">Children Attendance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parent/children/grades",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">Children Grades</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parent/messages",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">Messages</div>
          </ProtectedRoute>
        ),
      },

      // ===== COMMON ROUTES =====
      {
        path: "profile",
        element: (
          <ProtectedRoute>
            <div className="p-8">Profile</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "notifications",
        element: (
          <ProtectedRoute>
            <NotificationDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "notifications/:id",
        element: (
          <ProtectedRoute>
            <ViewNotification />
          </ProtectedRoute>
        ),
      },
      {
        path: "notifications/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <CreateNotification />
          </ProtectedRoute>
        ),
      },
      {
        path: "notifications/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <CreateNotification />
          </ProtectedRoute>
        ),
      },
      {
        path: "settings",
        element: (
          <ProtectedRoute>
            <Settings />
          </ProtectedRoute>
        ),
      },
    ],
  },
  {
    path: "*",
    element: <NotFoundPage />,
  },
]);

function App() {
  return (
    <>
      <Toaster />
      <RouterProvider router={router} />
    </>
  );
}

export default App;
