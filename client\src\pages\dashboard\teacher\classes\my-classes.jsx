import React, { useState, useEffect } from "react";
import { Container } from "@/components/ui/container";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Users,
  BookOpen,
  GraduationCap,
  Clock,
  Calendar,
  FileText,
  UserCheck,
  AlertCircle,
  ChevronRight,
  RefreshCcwIcon,
} from "lucide-react";
import { useTeacher } from "@/context/teacher-context";
import { useStudent } from "@/context/student-context";
import { StatCard } from "@/components/dashboard/stat-card";
import { Link } from "react-router-dom";

const MyClasses = () => {
  const { currentTeacher, fetchCurrentTeacherProfile, isLoading } =
    useTeacher();
  const { students, fetchAllStudents } = useStudent();

  const [selectedClass, setSelectedClass] = useState(null);
  const [classStudents, setClassStudents] = useState([]);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [stats, setStats] = useState({ total: 0, active: 0, totalStudents: 0 });

  useEffect(() => {
    fetchCurrentTeacherProfile();
    fetchAllStudents();
  }, []);

  const getAssignedClasses = () => {
    if (!currentTeacher?.assignedSections) return [];

    const classMap = new Map();
    currentTeacher.assignedSections.forEach((section) => {
      if (section.classId) {
        classMap.set(section.classId._id, section.classId);
      }
    });

    return Array.from(classMap.values());
  };

  const assignedClasses = getAssignedClasses();

  useEffect(() => {
    if (assignedClasses.length > 0 && !selectedClass) {
      setSelectedClass(assignedClasses[0]);
    }
  }, [currentTeacher, selectedClass]);

  useEffect(() => {
    if (
      selectedClass &&
      students.length > 0 &&
      currentTeacher?.assignedSections
    ) {
      setLoadingStudents(true);

      // Get sections assigned to this teacher for the selected class
      const teacherSectionsForClass = currentTeacher.assignedSections.filter(
        (section) =>
          section.classId?._id === selectedClass._id ||
          section.classId === selectedClass._id
      );

      // Filter students by selected class and teacher's assigned sections
      const filteredStudents = students.filter((student) => {
        const isInClass =
          student.class?._id === selectedClass._id ||
          student.class === selectedClass._id;
        const isInTeacherSection = teacherSectionsForClass.some(
          (section) =>
            student.section?._id === section._id ||
            student.section === section._id
        );
        return isInClass && isInTeacherSection && student.status === "active";
      });

      setClassStudents(filteredStudents);
      setLoadingStudents(false);
    }
  }, [selectedClass, students, currentTeacher]);

  // Calculate class stats in useEffect
  useEffect(() => {
    const getClassStats = () => {
      if (!assignedClasses.length)
        return { total: 0, active: 0, totalStudents: 0 };

      const totalClasses = assignedClasses.length;
      const activeClasses = assignedClasses.filter(
        (cls) => cls.isActive !== false
      ).length;
      const totalStudents = students.filter((student) => {
        if (student.status !== "active") return false;

        // Check if student is in any of the teacher's assigned sections
        return currentTeacher?.assignedSections?.some(
          (section) =>
            student.section?._id === section._id ||
            student.section === section._id
        );
      }).length;

      return { total: totalClasses, active: activeClasses, totalStudents };
    };

    setStats(getClassStats());
  }, [students]);

  const handleClassSelect = (classData) => {
    setSelectedClass(classData);
  };

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          title="My Classes"
          description="Manage your assigned classes and students"
          isLoading={isLoading}
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "My Classes" },
          ]}
          actions={[
            {
              label: "Refresh",
              icon: RefreshCcwIcon,
              onClick: () => window.location.reload(),
            },
          ]}
        />

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatCard
            title="Total Classes"
            value={stats.total}
            description="Assigned to you"
            icon={BookOpen}
            isLoading={isLoading}
          />
          <StatCard
            title="Active Classes"
            value={stats.active}
            description="Currently teaching"
            icon={GraduationCap}
            isLoading={isLoading}
          />
          <StatCard
            title="Total Students"
            value={stats.totalStudents}
            description="Across all classes"
            icon={Users}
            isLoading={isLoading}
          />
        </div>

        {isLoading ? (
          <Container className="py-8">
            <div className="space-y-6">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="h-32 bg-muted animate-pulse rounded"
                  />
                ))}
              </div>
            </div>
          </Container>
        ) : (
          <>
            {!assignedClasses || assignedClasses.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <BookOpen className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Classes Assigned
                  </h3>
                  <p className="text-muted-foreground text-center max-w-md">
                    You don't have any classes assigned yet. Please contact your
                    administrator to get classes assigned to your profile.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Class List Sidebar */}
                <div className="lg:col-span-1">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BookOpen className="h-5 w-5" />
                        My Classes ({assignedClasses.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-[400px]">
                        <div className="space-y-2">
                          {assignedClasses.map((classData) => (
                            <div
                              key={classData._id}
                              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                                selectedClass?._id === classData._id
                                  ? "bg-primary/5 border-primary"
                                  : "hover:bg-muted/50"
                              }`}
                              onClick={() => handleClassSelect(classData)}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <h4 className="font-medium">
                                    {classData.name}
                                  </h4>
                                  <p className="text-sm text-muted-foreground">
                                    {classData.code}
                                  </p>
                                </div>
                                <ChevronRight className="h-4 w-4 text-muted-foreground" />
                              </div>
                              <div className="mt-2 flex items-center gap-2">
                                <Badge variant="secondary" className="text-xs">
                                  {classData.academicYear}
                                </Badge>
                                {classData.department && (
                                  <Badge variant="outline" className="text-xs">
                                    {classData.department.name}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>

                {/* Class Details */}
                <div className="lg:col-span-2">
                  {selectedClass ? (
                    <div className="space-y-6">
                      {/* Class Header */}
                      <Card>
                        <CardHeader>
                          <div className="flex items-start justify-between">
                            <div>
                              <CardTitle className="text-2xl">
                                {selectedClass.name}
                              </CardTitle>
                              <p className="text-muted-foreground mt-1">
                                Class Code: {selectedClass.code}
                              </p>
                            </div>
                            <Badge
                              variant={
                                selectedClass.isActive !== false
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {selectedClass.isActive !== false
                                ? "Active"
                                : "Inactive"}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <span className="text-muted-foreground">
                                  Academic Year:
                                </span>
                                <span className="font-medium">
                                  {selectedClass.academicYear}
                                </span>
                              </div>
                              {selectedClass.department && (
                                <div className="flex items-center gap-2 text-sm">
                                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                                  <span className="text-muted-foreground">
                                    Department:
                                  </span>
                                  <span className="font-medium">
                                    {selectedClass.department.name}
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm">
                                <Users className="h-4 w-4 text-muted-foreground" />
                                <span className="text-muted-foreground">
                                  Students:
                                </span>
                                <span className="font-medium">
                                  {classStudents.length}
                                </span>
                              </div>
                              {currentTeacher?.assignedSections && (
                                <div className="flex items-center gap-2 text-sm">
                                  <GraduationCap className="h-4 w-4 text-muted-foreground" />
                                  <span className="text-muted-foreground">
                                    My Sections:
                                  </span>
                                  <div className="flex gap-1">
                                    {currentTeacher.assignedSections
                                      .filter(
                                        (section) =>
                                          section.classId?._id ===
                                            selectedClass._id ||
                                          section.classId === selectedClass._id
                                      )
                                      .map((section) => (
                                        <Badge
                                          key={section._id}
                                          variant="outline"
                                          className="text-xs"
                                        >
                                          {section.name}
                                        </Badge>
                                      ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                          {selectedClass.description && (
                            <div className="mt-4">
                              <p className="text-sm text-muted-foreground">
                                {selectedClass.description}
                              </p>
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      {/* Class Actions */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Button asChild className="h-auto p-4">
                          <Link to="/dashboard/teacher/attendance">
                            <div className="flex flex-col items-center gap-2">
                              <UserCheck className="h-6 w-6" />
                              <span>Take Attendance</span>
                            </div>
                          </Link>
                        </Button>
                        <Button
                          asChild
                          variant="outline"
                          className="h-auto p-4"
                        >
                          <Link to="/dashboard/teacher/assignments">
                            <div className="flex flex-col items-center gap-2">
                              <FileText className="h-6 w-6" />
                              <span>Assignments</span>
                            </div>
                          </Link>
                        </Button>
                        <Button
                          asChild
                          variant="outline"
                          className="h-auto p-4"
                        >
                          <Link to="/dashboard/teacher/timetable">
                            <div className="flex flex-col items-center gap-2">
                              <Clock className="h-6 w-6" />
                              <span>View Timetable</span>
                            </div>
                          </Link>
                        </Button>
                      </div>

                      {/* Students List */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Users className="h-5 w-5" />
                            Students ({classStudents.length})
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          {loadingStudents ? (
                            <div className="space-y-3">
                              {[1, 2, 3].map((i) => (
                                <div
                                  key={i}
                                  className="h-16 bg-muted animate-pulse rounded"
                                />
                              ))}
                            </div>
                          ) : classStudents.length === 0 ? (
                            <div className="text-center py-8">
                              <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                              <h3 className="text-lg font-medium mb-2">
                                No Students Found
                              </h3>
                              <p className="text-muted-foreground">
                                No active students are enrolled in this class.
                              </p>
                            </div>
                          ) : (
                            <ScrollArea className="h-[300px]">
                              <div className="space-y-3">
                                {classStudents.map((student) => (
                                  <div
                                    key={student._id}
                                    className="flex items-center gap-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                                  >
                                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                                      <span className="text-sm font-medium text-primary">
                                        {student.firstName?.charAt(0)}
                                        {student.lastName?.charAt(0)}
                                      </span>
                                    </div>
                                    <div className="flex-1">
                                      <div className="flex items-center gap-2">
                                        <h4 className="font-medium">
                                          {student.firstName} {student.lastName}
                                        </h4>
                                        <Badge
                                          variant="outline"
                                          className="text-xs"
                                        >
                                          {student.rollNumber}
                                        </Badge>
                                      </div>
                                      <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                                        <span>{student.email}</span>
                                        {student.section && (
                                          <>
                                            <span>•</span>
                                            <span>
                                              Section:{" "}
                                              {typeof student.section ===
                                              "object"
                                                ? student.section.name
                                                : student.section}
                                            </span>
                                          </>
                                        )}
                                      </div>
                                    </div>
                                    <Button asChild variant="ghost" size="sm">
                                      <Link
                                        to={`/dashboard/students/${student._id}`}
                                      >
                                        View Profile
                                      </Link>
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </ScrollArea>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  ) : (
                    <Card>
                      <CardContent className="flex flex-col items-center justify-center py-12">
                        <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          Select a Class
                        </h3>
                        <p className="text-muted-foreground text-center">
                          Choose a class from the sidebar to view details and
                          manage students.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}
          </>
        )}
      </main>
    </div>
  );
};

export default MyClasses;
